// test-carousel.component.ts
import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CarouselPlayerComponent, CarouselState } from './components/carousel-player.component';
import { Playlist, PlaylistItem } from '../../core/models/playlist.model';

@Component({
  selector: 'app-test-carousel',
  standalone: true,
  imports: [CommonModule, CarouselPlayerComponent],
  template: `
    <div class="test-container">
      <h2>Carousel Player Test</h2>
      
      <div class="controls">
        <button (click)="startTest()" [disabled]="isPlaying">Start Test</button>
        <button (click)="pauseTest()" [disabled]="!isPlaying">Pause</button>
        <button (click)="resumeTest()" [disabled]="!isPaused">Resume</button>
        <button (click)="stopTest()">Stop</button>
        <button (click)="nextItem()">Next</button>
        <button (click)="prevItem()">Previous</button>
      </div>

      <div class="status" *ngIf="carouselState">
        <p>Status: {{ isPlaying ? 'Playing' : (isPaused ? 'Paused' : 'Stopped') }}</p>
        <p>Item: {{ carouselState.currentIndex + 1 }} / {{ carouselState.totalItems }}</p>
        <p>Progress: {{ carouselState.progress.toFixed(1) }}%</p>
        <p>Time Remaining: {{ carouselState.timeRemaining.toFixed(1) }}s</p>
        <p>Current Item: {{ carouselState.currentItem?.name || 'None' }}</p>
      </div>

      <div class="carousel-container">
        <app-carousel-player
          *ngIf="testPlaylist"
          #carouselPlayer
          [playlist]="testPlaylist"
          [autoPlay]="false"
          [loop]="true"
          [transitionType]="'fade'"
          [transitionDuration]="500"
          [showProgress]="true"
          (stateChange)="onStateChange($event)"
          (playlistEnded)="onPlaylistEnded()"
          (error)="onError($event)">
        </app-carousel-player>
      </div>

      <div class="error" *ngIf="errorMessage">
        <p>Error: {{ errorMessage }}</p>
      </div>
    </div>
  `,
  styles: [`
    .test-container {
      padding: 20px;
      max-width: 1200px;
      margin: 0 auto;
    }

    .controls {
      margin: 20px 0;
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
    }

    .controls button {
      padding: 10px 20px;
      background: #007bff;
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
    }

    .controls button:disabled {
      background: #ccc;
      cursor: not-allowed;
    }

    .controls button:hover:not(:disabled) {
      background: #0056b3;
    }

    .status {
      background: #f8f9fa;
      padding: 15px;
      border-radius: 5px;
      margin: 20px 0;
    }

    .status p {
      margin: 5px 0;
    }

    .carousel-container {
      width: 100%;
      height: 400px;
      border: 2px solid #ddd;
      border-radius: 10px;
      overflow: hidden;
      background: #000;
    }

    .error {
      background: #f8d7da;
      color: #721c24;
      padding: 15px;
      border-radius: 5px;
      margin: 20px 0;
    }

    h2 {
      color: #333;
      margin-bottom: 20px;
    }
  `]
})
export class TestCarouselComponent implements OnInit {
  testPlaylist: Playlist | null = null;
  carouselState: CarouselState | null = null;
  errorMessage: string | null = null;
  isPlaying = false;
  isPaused = false;

  ngOnInit(): void {
    this.createTestPlaylist();
  }

  private createTestPlaylist(): void {
    // Create a test playlist with various media types
    const testItems: PlaylistItem[] = [
      {
        id: '1',
        type: 'image',
        name: 'Test Image 1',
        duration: 5,
        content: {
          url: 'https://picsum.photos/1920/1080?random=1'
        },
        settings: {
          transition: 'fade',
          transitionDuration: 500,
          scaling: 'stretch'
        },
        schedule: null
      },
      {
        id: '2',
        type: 'image',
        name: 'Test Image 2',
        duration: 3,
        content: {
          url: 'https://picsum.photos/1920/1080?random=2'
        },
        settings: {
          transition: 'fade',
          transitionDuration: 500,
          scaling: 'stretch'
        },
        schedule: null
      },
      {
        id: '3',
        type: 'webpage',
        name: 'Test Web Page',
        duration: 8,
        content: {
          url: 'https://www.example.com'
        },
        settings: {
          transition: 'slide',
          transitionDuration: 500,
          scaling: 'stretch'
        },
        schedule: null
      },
      {
        id: '4',
        type: 'image',
        name: 'Test Image 3',
        duration: 4,
        content: {
          url: 'https://picsum.photos/1920/1080?random=3'
        },
        settings: {
          transition: 'fade',
          transitionDuration: 500,
          scaling: 'stretch'
        },
        schedule: null
      }
    ];

    this.testPlaylist = {
      id: 'test-playlist',
      name: 'Test Carousel Playlist',
      description: 'A test playlist for carousel functionality',
      duration: testItems.reduce((total, item) => total + item.duration, 0),
      items: testItems,
      lastModified: new Date().toISOString(),
      createdBy: 'test-user',
      status: 'active',
      tags: ['test'],
      settings: {
        autoPlay: true,
        loop: true,
        defaultMuted: true,
        transition: {
          type: 'fade',
          duration: 0.5
        },
        defaultDuration: 5,
        scheduling: {
          enabled: false,
          priority: 1
        }
      }
    };
  }

  startTest(): void {
    if (this.testPlaylist) {
      // This would trigger the carousel to start
      this.isPlaying = true;
      this.isPaused = false;
      console.log('Starting carousel test');
    }
  }

  pauseTest(): void {
    this.isPlaying = false;
    this.isPaused = true;
    console.log('Pausing carousel test');
  }

  resumeTest(): void {
    this.isPlaying = true;
    this.isPaused = false;
    console.log('Resuming carousel test');
  }

  stopTest(): void {
    this.isPlaying = false;
    this.isPaused = false;
    console.log('Stopping carousel test');
  }

  nextItem(): void {
    console.log('Next item requested');
  }

  prevItem(): void {
    console.log('Previous item requested');
  }

  onStateChange(state: CarouselState): void {
    this.carouselState = state;
    this.isPlaying = state.isPlaying;
    this.isPaused = state.isPaused;
  }

  onPlaylistEnded(): void {
    console.log('Playlist ended');
    this.isPlaying = false;
    this.isPaused = false;
  }

  onError(error: string): void {
    this.errorMessage = error;
    console.error('Carousel error:', error);
  }
}
