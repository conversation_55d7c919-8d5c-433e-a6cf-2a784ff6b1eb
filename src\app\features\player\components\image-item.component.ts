// image-item.component.ts
import { Component, Input, Output, EventEmitter, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PlaylistItem } from '../../../core/models/playlist.model';
import { LogService } from '../../../core/services/log.service';
import { ContentSyncService } from '../../../core/services/content-sync.service';

@Component({
  selector: 'app-image-item',
  standalone: true,
  imports: [CommonModule],
  template: `
    <img 
      *ngIf="localImageUrl"
      [src]="localImageUrl" 
      [alt]="item?.name || 'Image content'" 
      (load)="onImageLoaded()" 
      (error)="onImageError()"
      class="fullscreen-image"
    />
    <div *ngIf="!localImageUrl && !loading" class="error-placeholder">
      <span class="material-icons">broken_image</span>
      <p>Image could not be loaded</p>
    </div>
    <div *ngIf="loading" class="loading-indicator">
      <div class="spinner"></div>
    </div>
  `,
  styles: [`
    :host {
      display: block !important;
      width: 100vw !important;
      height: 100vh !important;
      overflow: hidden !important;
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      margin: 0 !important;
      padding: 0 !important;
      background: #000 !important;
      z-index: 1 !important;
    }
    
    .fullscreen-image {
      display: block !important;
      width: 100vw !important;
      height: 100vh !important;
      object-fit: fill !important;
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      margin: 0 !important;
      padding: 0 !important;
      border: none !important;
      outline: none !important;
      max-width: none !important;
      max-height: none !important;
      min-width: 100vw !important;
      min-height: 100vh !important;
      z-index: 2 !important;
    }
    
    .error-placeholder {
      display: flex !important;
      flex-direction: column !important;
      align-items: center !important;
      justify-content: center !important;
      color: #dc3545 !important;
      width: 100vw !important;
      height: 100vh !important;
      margin: 0 !important;
      padding: 0 !important;
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      background: #000 !important;
      z-index: 2 !important;
      
      .material-icons {
        font-size: 4rem !important;
        margin-bottom: 1rem !important;
      }
    }
    
    .loading-indicator {
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      width: 100vw !important;
      height: 100vh !important;
      margin: 0 !important;
      padding: 0 !important;
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      background: #000 !important;
      z-index: 2 !important;
      
      .spinner {
        width: 50px !important;
        height: 50px !important;
        border: 5px solid rgba(255, 255, 255, 0.3) !important;
        border-radius: 50% !important;
        border-top-color: #fff !important;
        animation: spin 1s ease-in-out infinite !important;
      }
    }
    
    @keyframes spin {
      to { transform: rotate(360deg); }
    }
  `]
})
export class ImageItemComponent implements OnInit, OnDestroy {
  @Input() item: PlaylistItem | null = null;
  @Input() scaling: 'fit' | 'fill' | 'stretch' = 'stretch';
  @Input() preload = false; // If true, just preload but don't start timer
  @Output() ended = new EventEmitter<void>();
  
  localImageUrl: string | null = null;
  loading = true;
  private timer: any;
  private originalUrl: string | null = null;
  
  constructor(
    private contentSyncService: ContentSyncService,
    private logService: LogService
  ) {}
  
  ngOnInit(): void {
    this.loading = true; // Make sure this is reset
    this.localImageUrl = null; // Reset the URL
    
    if (!this.item) {
      this.loading = false;
      return;
    }
    
    // Store the original URL to use as fallback if needed
    this.originalUrl = this.item.content.url;
    
    this.loadImage();
  }
  
  ngOnDestroy(): void {
    this.clearTimer();
    
    // Clear the image source to prevent memory leaks
    this.localImageUrl = null;
  }
  
  get scalingClass(): string {
    return this.scaling || 'stretch';
  }
  
  onImageLoaded(): void {
    this.loading = false;
    this.logService.info(`Image loaded: ${this.item?.name}`);
    
    // If not in preload mode, start the timer
    if (!this.preload && this.item) {
      this.startTimer();
    }
  }
  
  onImageError(): void {
    this.loading = false;
    
    // Log error with the attempted URL
    this.logService.error(`Failed to load image: ${this.localImageUrl}`);
    
    // If using a cached URL that failed, try the original URL directly
    if (this.localImageUrl !== this.originalUrl && this.originalUrl) {
      this.logService.info(`Trying original URL: ${this.originalUrl}`);
      this.localImageUrl = this.originalUrl;
      return; // Don't start timer yet, wait for the image to load or error again
    }
    
    this.localImageUrl = null;
    
    // Even on error, we need to emit ended event after duration
    if (!this.preload && this.item) {
      this.startTimer();
    }
  }
  
  private loadImage(): void {
    if (!this.item) return;
    
    // First check if we have a local cached version
    this.contentSyncService.getLocalContentUrl(this.item.content.url).subscribe(
      localUrl => {
        this.localImageUrl = localUrl;
        // We'll set loading to false after the image loads
      },
      error => {
        this.logService.error(`Error getting local content: ${error.message}`);
        // Fall back to direct URL
        this.localImageUrl = this.item!.content.url;
      }
    );
  }
  
  private startTimer(): void {
    // Clear any existing timer
    this.clearTimer();
    
    // Set timer for the duration of this item
    if (this.item && this.item.duration > 0) {
      this.timer = setTimeout(() => {
        this.ended.emit();
      }, this.item.duration * 1000); // Convert seconds to milliseconds
    }
  }
  
  private clearTimer(): void {
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
  }
}